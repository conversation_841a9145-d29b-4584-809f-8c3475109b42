<?php
/**
 * @package     Joomla.Site
 * @subpackage  Templates.zenbase
 *
 * @copyright   Copyright (C) 2005 - 2023 Open Source Matters, Inc. All rights reserved.
 * @license     GNU General Public License version 2 or later; see LICENSE.txt
 */

defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Factory;
use <PERSON><PERSON><PERSON>\CMS\Helper\AuthenticationHelper;
use <PERSON><PERSON><PERSON>\CMS\HTML\HTMLHelper;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;
use <PERSON><PERSON><PERSON>\CMS\Router\Route;
use Joomla\CMS\Uri\Uri;

$app = Factory::getApplication();
$doc = Factory::getDocument();
$user = Factory::getUser();
$this->language = $doc->language;
$this->direction = $doc->direction;

// Template URL
$template_url = $this->baseurl . '/templates/' . $this->template;

// Detecting Active Variables
$option   = $app->input->getCmd('option', '');
$view     = $app->input->getCmd('view', '');
$layout   = $app->input->getCmd('layout', '');
$task     = $app->input->getCmd('task', '');
$itemid   = $app->input->getCmd('Itemid', '');
$sitename = htmlspecialchars($app->get('sitename'), ENT_QUOTES, 'UTF-8');
$menu     = $app->getMenu()->getActive();
$pageclass = $menu !== null ? $menu->getParams()->get('pageclass_sfx', '') : '';

// Logo file or site title param
if ($this->params->get('logoFile'))
{
	$logo = '<img src="' . Uri::root() . htmlspecialchars($this->params->get('logoFile'), ENT_QUOTES) . '" alt="' . $sitename . '">';
}
elseif ($this->params->get('sitetitle'))
{
	$logo = '<span title="' . $sitename . '">' . htmlspecialchars($this->params->get('sitetitle'), ENT_COMPAT, 'UTF-8') . '</span>';
}
else
{
	$logo = '<span title="' . $sitename . '">' . $sitename . '</span>';
}

// Add Stylesheets - Only essential ones for maintenance page
HTMLHelper::_('bootstrap.framework');
$doc->addStyleSheet($template_url . '/css/template.css');

// Add minimal JavaScript - avoid difficulty modal scripts
$doc->addScript($template_url . '/js/css_browser_selector.js');

// Add inline styles for centering the maintenance page
$doc->addStyleDeclaration('
    body {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    #frame.outline {
        background: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 400px;
        width: 100%;
        text-align: center;
    }

    #frame.outline h1 {
        color: #333;
        margin-bottom: 20px;
        font-size: 24px;
    }

    #frame.outline p {
        color: #666;
        margin-bottom: 30px;
        line-height: 1.5;
    }

    #form-login {
        text-align: left;
    }

    #form-login label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }

    #form-login input[type="text"],
    #form-login input[type="password"] {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 15px;
        font-size: 14px;
        box-sizing: border-box;
    }

    #form-login input[type="submit"] {
        width: 100%;
        padding: 12px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 10px;
    }

    #form-login input[type="submit"]:hover {
        background-color: #0056b3;
    }

    #form-login input[type="checkbox"] {
        margin-right: 8px;
    }

    #form-login-remember {
        margin-bottom: 20px;
    }

    #form-login-remember label {
        display: inline;
        font-weight: normal;
    }
');

?>
<!DOCTYPE html>
<html lang="<?php echo $this->language; ?>" dir="<?php echo $this->direction; ?>">
<head>
	<jdoc:include type="head" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="apple-touch-icon" sizes="180x180" href="<?= $template_url; ?>/images/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="<?= $template_url; ?>/images/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="<?= $template_url; ?>/images/favicon-16x16.png">
	<link rel="manifest" href="<?= $template_url; ?>/images/site.webmanifest">
	<link rel="mask-icon" href="<?= $template_url; ?>/images/safari-pinned-tab.svg" color="#0095cc">
</head>

<body class="<?php echo $option
	. ' view-' . $view
	. ($layout ? ' layout-' . $layout : ' no-layout')
	. ($task ? ' task-' . $task : ' no-task')
	. ($itemid ? ' itemid-' . $itemid : '')
	. ($pageclass ? ' ' . $pageclass : '')
	. ($this->direction == 'rtl' ? ' rtl' : '');
?>">
	<jdoc:include type="message" />
	<div id="frame" class="outline">
		<?php if ($this->params->get('logoFile') || $this->params->get('sitetitle')) : ?>
			<div id="logo">
				<?php echo $logo; ?>
			</div>
		<?php else : ?>
			<h1>
				<?php echo htmlspecialchars($app->get('sitename'), ENT_QUOTES, 'UTF-8'); ?>
			</h1>
		<?php endif; ?>
		<?php if ($app->get('offline_message')) : ?>
			<p>
				<?php echo $app->get('offline_message'); ?>
			</p>
		<?php else : ?>
			<p>
				<?php echo Text::_('JOFFLINE_MESSAGE'); ?>
			</p>
		<?php endif; ?>
		<form action="<?php echo Route::_('index.php', true); ?>" method="post" id="form-login">
			<fieldset class="input">
				<div>
					<label for="username"><?php echo Text::_('JGLOBAL_USERNAME'); ?></label>
					<input name="username" id="username" type="text" class="inputbox" alt="<?php echo Text::_('JGLOBAL_USERNAME'); ?>" autocomplete="off" autocapitalize="none" />
				</div>
				<div>
					<label for="passwd"><?php echo Text::_('JGLOBAL_PASSWORD'); ?></label>
					<input type="password" name="password" class="inputbox" alt="<?php echo Text::_('JGLOBAL_PASSWORD'); ?>" id="passwd" />
				</div>
				<?php if (count($twofactormethods = AuthenticationHelper::getTwoFactorMethods()) > 1) : ?>
					<div>
						<label for="secretkey"><?php echo Text::_('JGLOBAL_SECRETKEY'); ?></label>
						<input type="text" name="secretkey" class="inputbox" alt="<?php echo Text::_('JGLOBAL_SECRETKEY'); ?>" id="secretkey" />
					</div>
				<?php endif; ?>
				<?php if (JPluginHelper::isEnabled('system', 'remember')) : ?>
					<div id="form-login-remember">
						<input type="checkbox" name="remember" class="inputbox" value="yes" alt="<?php echo Text::_('JGLOBAL_REMEMBER_ME'); ?>" id="remember" />
						<label for="remember"><?php echo Text::_('JGLOBAL_REMEMBER_ME'); ?></label>
					</div>
				<?php endif; ?>
				<div>
					<input type="submit" name="Submit" class="button login" value="<?php echo Text::_('JLOGIN'); ?>" />
				</div>
				<input type="hidden" name="option" value="com_users" />
				<input type="hidden" name="task" value="user.login" />
				<input type="hidden" name="return" value="<?php echo base64_encode(Uri::base()); ?>" />
				<?php echo HTMLHelper::_('form.token'); ?>
			</fieldset>
		</form>
	</div>
</body>
</html>
