<?php
/**
 * @package     Joomla.Site
 * @subpackage  Templates.zenbase
 *
 * @copyright   Copyright (C) 2005 - 2023 Open Source Matters, Inc. All rights reserved.
 * @license     GNU General Public License version 2 or later; see LICENSE.txt
 */

defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Factory;
use <PERSON><PERSON><PERSON>\CMS\Helper\AuthenticationHelper;
use <PERSON><PERSON><PERSON>\CMS\HTML\HTMLHelper;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;
use <PERSON><PERSON><PERSON>\CMS\Router\Route;
use Joomla\CMS\Uri\Uri;

$app = Factory::getApplication();
$doc = Factory::getDocument();
$user = Factory::getUser();
$this->language = $doc->language;
$this->direction = $doc->direction;

// Template URL
$template_url = $this->baseurl . '/templates/' . $this->template;

// Detecting Active Variables
$option   = $app->input->getCmd('option', '');
$view     = $app->input->getCmd('view', '');
$layout   = $app->input->getCmd('layout', '');
$task     = $app->input->getCmd('task', '');
$itemid   = $app->input->getCmd('Itemid', '');
$sitename = htmlspecialchars($app->get('sitename'), ENT_QUOTES, 'UTF-8');
$menu     = $app->getMenu()->getActive();
$pageclass = $menu !== null ? $menu->getParams()->get('pageclass_sfx', '') : '';

// Logo file or site title param
if ($this->params->get('logoFile'))
{
	$logo = '<img src="' . Uri::root() . htmlspecialchars($this->params->get('logoFile'), ENT_QUOTES) . '" alt="' . $sitename . '">';
}
elseif ($this->params->get('sitetitle'))
{
	$logo = '<span title="' . $sitename . '">' . htmlspecialchars($this->params->get('sitetitle'), ENT_COMPAT, 'UTF-8') . '</span>';
}
else
{
	$logo = '<span title="' . $sitename . '">' . $sitename . '</span>';
}

// Add Stylesheets - Only essential ones for maintenance page
HTMLHelper::_('bootstrap.framework');
$doc->addStyleSheet($template_url . '/css/template.css');

// Add minimal JavaScript - avoid difficulty modal scripts
$doc->addScript($template_url . '/js/css_browser_selector.js');

?>
<!DOCTYPE html>
<html lang="<?php echo $this->language; ?>" dir="<?php echo $this->direction; ?>">
<head>
	<jdoc:include type="head" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="apple-touch-icon" sizes="180x180" href="<?= $template_url; ?>/images/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="<?= $template_url; ?>/images/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="<?= $template_url; ?>/images/favicon-16x16.png">
	<link rel="manifest" href="<?= $template_url; ?>/images/site.webmanifest">
	<link rel="mask-icon" href="<?= $template_url; ?>/images/safari-pinned-tab.svg" color="#0095cc">
</head>

<body class="<?php echo $option
	. ' view-' . $view
	. ($layout ? ' layout-' . $layout : ' no-layout')
	. ($task ? ' task-' . $task : ' no-task')
	. ($itemid ? ' itemid-' . $itemid : '')
	. ($pageclass ? ' ' . $pageclass : '')
	. ($this->direction == 'rtl' ? ' rtl' : '');
?>">
	<jdoc:include type="message" />
	<div id="frame" class="outline">
		<?php if ($this->params->get('logoFile') || $this->params->get('sitetitle')) : ?>
			<div id="logo">
				<?php echo $logo; ?>
			</div>
		<?php else : ?>
			<h1>
				<?php echo htmlspecialchars($app->get('sitename'), ENT_QUOTES, 'UTF-8'); ?>
			</h1>
		<?php endif; ?>
		<?php if ($app->get('offline_message')) : ?>
			<p>
				<?php echo $app->get('offline_message'); ?>
			</p>
		<?php else : ?>
			<p>
				<?php echo Text::_('JOFFLINE_MESSAGE'); ?>
			</p>
		<?php endif; ?>
		<div id="form-login-username">
			<div id="form-login-password">
				<div id="form-login-remember">
					<div id="submit-buton">
						<form action="<?php echo Route::_('index.php', true); ?>" method="post" id="form-login">
							<fieldset class="input">
								<p id="form-login-username">
									<label for="username"><?php echo Text::_('JGLOBAL_USERNAME'); ?></label>
									<input name="username" id="username" type="text" class="inputbox" alt="<?php echo Text::_('JGLOBAL_USERNAME'); ?>" autocomplete="off" autocapitalize="none" />
								</p>
								<p id="form-login-password">
									<label for="passwd"><?php echo Text::_('JGLOBAL_PASSWORD'); ?></label>
									<input type="password" name="password" class="inputbox" alt="<?php echo Text::_('JGLOBAL_PASSWORD'); ?>" id="passwd" />
								</p>
								<?php if (count($twofactormethods = AuthenticationHelper::getTwoFactorMethods()) > 1) : ?>
									<p id="form-login-secretkey">
										<label for="secretkey"><?php echo Text::_('JGLOBAL_SECRETKEY'); ?></label>
										<input type="text" name="secretkey" class="inputbox" alt="<?php echo Text::_('JGLOBAL_SECRETKEY'); ?>" id="secretkey" />
									</p>
								<?php endif; ?>
								<?php if (JPluginHelper::isEnabled('system', 'remember')) : ?>
									<p id="form-login-remember">
										<label for="remember"><?php echo Text::_('JGLOBAL_REMEMBER_ME'); ?></label>
										<input type="checkbox" name="remember" class="inputbox" value="yes" alt="<?php echo Text::_('JGLOBAL_REMEMBER_ME'); ?>" id="remember" />
									</p>
								<?php endif; ?>
								<p id="submit-buton">
									<input type="submit" name="Submit" class="button login" value="<?php echo Text::_('JLOGIN'); ?>" />
								</p>
								<input type="hidden" name="option" value="com_users" />
								<input type="hidden" name="task" value="user.login" />
								<input type="hidden" name="return" value="<?php echo base64_encode(Uri::base()); ?>" />
								<?php echo HTMLHelper::_('form.token'); ?>
							</fieldset>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
