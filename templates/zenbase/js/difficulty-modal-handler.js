/**
 * Difficulty Modal Handler
 * This script ensures that difficulty modals are properly initialized and accessible
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Difficulty Modal Handler loaded');

    // Check if we're on the offline/maintenance login page - if so, don't initialize modals
    if (document.body.classList.contains('offline-login-page') ||
        document.body.getAttribute('data-page-type') === 'offline-login') {
        console.log('Offline login page detected - skipping difficulty modal initialization');
        return;
    }

    // Clean up any existing backdrops (in case of page refresh with orphaned backdrops)
    cleanupOrphanedBackdrops();

    // Debug: Check if global modals exist
    const globalModalsContainer = document.querySelector('.global-difficulty-modals');
    if (globalModalsContainer) {
        console.log('Global difficulty modals container found');
        const modalElements = globalModalsContainer.querySelectorAll('.difficulty-modal');
        console.log(`Found ${modalElements.length} difficulty modals`);
        modalElements.forEach(modal => {
            console.log(`Modal ID: ${modal.id}`);

            // Add event listeners for modal events
            modal.addEventListener('show.bs.modal', handleModalShow);
            modal.addEventListener('hidden.bs.modal', handleModalHidden);
        });
    } else {
        console.warn('Global difficulty modals container not found!');
        // If global modals don't exist, create them
        createGlobalModals();
    }

    // Initialize difficulty modals
    initializeDifficultyModals();

    // Function to handle modal show event
    function handleModalShow(event) {
        console.log(`Modal ${event.target.id} is being shown`);

        // Only clean up extra backdrops if there are more than one
        const backdrops = document.querySelectorAll('.modal-backdrop');
        if (backdrops.length > 1) {
            console.log(`Found ${backdrops.length} backdrops, cleaning up extras`);
            // Keep only the last backdrop (most recently added)
            for (let i = 0; i < backdrops.length - 1; i++) {
                console.log(`Removing extra backdrop ${i+1}`);
                backdrops[i].remove();
            }
        }
    }

    // Function to handle modal hidden event
    function handleModalHidden(event) {
        console.log(`Modal ${event.target.id} has been hidden`);

        // Clean up all backdrops after the modal is hidden
        setTimeout(() => {
            cleanupOrphanedBackdrops();
        }, 150); // Small delay to ensure Bootstrap has finished its own cleanup
    }

    // Function to clean up orphaned backdrops
    function cleanupOrphanedBackdrops() {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        console.log(`Found ${backdrops.length} backdrops to clean up`);

        // Check if any modals are currently visible
        const visibleModals = document.querySelectorAll('.modal.show');

        if (visibleModals.length === 0 && backdrops.length > 0) {
            // No visible modals but backdrops exist - these are orphaned
            console.log('No visible modals but backdrops exist - cleaning up orphaned backdrops');
            backdrops.forEach(backdrop => {
                console.log('Removing backdrop');
                backdrop.remove();
            });

            // Also remove modal-open class from body if no modals are visible
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        } else if (visibleModals.length > 0 && backdrops.length > 1) {
            // Multiple backdrops with visible modals - keep only one backdrop
            console.log('Multiple backdrops with visible modals - keeping only the most recent backdrop');
            // Keep only the last backdrop (most recently added)
            for (let i = 0; i < backdrops.length - 1; i++) {
                console.log(`Removing extra backdrop ${i+1}`);
                backdrops[i].remove();
            }
        }
    }

    // Add event listener for Angular content loaded
    document.addEventListener('DOMNodeInserted', function(e) {
        if (e.target.classList && e.target.classList.contains('difficulty-info-btn')) {
            // Fix modal target for Angular-generated buttons
            fixAngularModalTargets();
        }
    });

    // Function to create global modals if they don't exist
    function createGlobalModals() {
        console.log('Creating global difficulty modals');

        // Define all difficulty levels with their descriptions and images
        const difficultyLevels = {
            'gentle': {
                'title': 'Gentle',
                'description': 'Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.',
                'image': 'grade-1.svg'
            },
            'easy': {
                'title': 'Easy',
                'description': 'Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.',
                'image': 'grade-1.svg'
            },
            'moderate': {
                'title': 'Moderate',
                'description': 'Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.',
                'image': 'grade-2.svg'
            },
            'challenging': {
                'title': 'Challenging',
                'description': 'For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.',
                'image': 'grade-3.svg'
            },
            'hardcore': {
                'title': 'Hardcore',
                'description': 'Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.',
                'image': 'grade-4.svg'
            }
        };

        // Create container for global modals
        const container = document.createElement('div');
        container.className = 'global-difficulty-modals';

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .difficulty-modal .modal-dialog {
                max-width: 400px;
            }
            .difficulty-modal .modal-content {
                border-radius: 24px;
                border: none;
                padding: 24px;
                background-color: #FFFFFF;
            }
            .difficulty-modal .modal-header {
                border: none;
                padding: 0;
                position: relative;
            }
            .difficulty-modal .btn-close {
                position: absolute;
                right: 15px;
                top: 15px;
                background: none;
                opacity: 1;
                width: 24px;
                height: 24px;
                padding: 0;
                z-index: 2;
            }
            .difficulty-modal .btn-close::before {
                content: "×";
                font-size: 32px;
                line-height: 24px;
                color: #000;
            }
            .difficulty-modal .modal-body {
                text-align: center;
                padding: 0;
            }
            .difficulty-modal .grade-image {
                width: 120px;
                height: 120px;
                margin: 24px auto;
            }
            .difficulty-modal .difficulty-title {
                font-size: 32px;
                font-weight: 500;
                margin-bottom: 24px;
                color: #000000;
            }
            .difficulty-modal .difficulty-description {
                font-size: 16px;
                line-height: 1.5;
                color: #333;
                margin-bottom: 32px;
            }
            .difficulty-modal .more-info-btn {
                background: #FE7720;
                color: white;
                border: 1px solid #FE7720;
                border-radius: 100px;
                padding: 11px 23px;
                font-size: 16px;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                transition: background-color 0.2s, border-color 0.2s;
                height: 48px;
                box-sizing: border-box;
            }
            .difficulty-modal .more-info-btn:hover {
                background: #e66b1c;
                border-color: #e66b1c;
            }
            .difficulty-modal .more-info-btn::after {
                content: "›";
                font-size: 20px;
                line-height: 1;
            }
        `;
        container.appendChild(style);

        // Create modals for each difficulty level
        for (const [level, data] of Object.entries(difficultyLevels)) {
            const modalHtml = `
                <div class="modal fade difficulty-modal" id="global-difficultyModal-${level}" tabindex="-1" aria-labelledby="difficultyModalLabel-${level}" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <img src="/templates/zenbase/images/grading/${data.image}" alt="${data.title} difficulty grade" class="grade-image">
                                <h2 class="difficulty-title">${data.title}</h2>
                                <div class="difficulty-description">
                                    ${data.description}
                                </div>
                                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                                    More about difficulty ratings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += modalHtml;
        }

        // Append to body
        document.body.appendChild(container);
        console.log('Global difficulty modals created and added to the page');

        // Add event listeners to the dynamically created modals
        const modalElements = container.querySelectorAll('.difficulty-modal');
        modalElements.forEach(modal => {
            console.log(`Adding event listeners to dynamically created modal: ${modal.id}`);
            modal.addEventListener('show.bs.modal', handleModalShow);
            modal.addEventListener('hidden.bs.modal', handleModalHidden);
        });
    }

    // Function to initialize difficulty modals
    function initializeDifficultyModals() {
        console.log('Initializing difficulty modals');

        // Get all difficulty levels
        const difficultyLevels = ['gentle', 'easy', 'moderate', 'challenging', 'hardcore'];

        // Create a mapping of difficulty levels to their modal IDs
        const modalMap = {};
        difficultyLevels.forEach(level => {
            modalMap[level] = `global-difficultyModal-${level}`;
        });

        // Store the mapping in the window object for global access
        window.difficultyModalMap = modalMap;

        // Debug: Check all difficulty buttons
        const allButtons = document.querySelectorAll('.difficulty-info-btn');
        console.log(`Found ${allButtons.length} difficulty buttons`);
        allButtons.forEach((button, index) => {
            const target = button.getAttribute('data-bs-target');
            console.log(`Button ${index} target: ${target}`);
        });

        // Add click event listeners to all difficulty buttons
        document.addEventListener('click', function(e) {
            const button = e.target.closest('.difficulty-info-btn');
            if (!button) return;

            e.preventDefault();
            e.stopPropagation();

            // Get the target modal ID
            let targetId = button.getAttribute('data-bs-target');
            if (!targetId) {
                console.warn('Button has no data-bs-target attribute');
                return;
            }

            console.log(`Button clicked with target: ${targetId}`);

            // Remove the # from the ID if it exists
            targetId = targetId.replace('#', '');

            // Find the modal element
            const modalElement = document.getElementById(targetId);
            if (!modalElement) {
                console.error(`Modal element with ID ${targetId} not found`);

                // Try to find a matching difficulty level
                const difficultyText = button.querySelector('.difficulty-text');
                if (difficultyText) {
                    const difficultyLevel = difficultyText.textContent.trim().toLowerCase();
                    console.log(`Extracted difficulty level: ${difficultyLevel}`);
                    const mappedModalId = window.difficultyModalMap[difficultyLevel];

                    if (mappedModalId) {
                        console.log(`Remapping to modal ID: ${mappedModalId}`);
                        const mappedModal = document.getElementById(mappedModalId);
                        if (mappedModal) {
                            console.log(`Found mapped modal, showing it`);

                            // Only clean up extra backdrops if there are more than one
                            const backdrops = document.querySelectorAll('.modal-backdrop');
                            if (backdrops.length > 1) {
                                console.log(`Found ${backdrops.length} backdrops, cleaning up extras`);
                                // Keep only the last backdrop (most recently added)
                                for (let i = 0; i < backdrops.length - 1; i++) {
                                    console.log(`Removing extra backdrop ${i+1}`);
                                    backdrops[i].remove();
                                }
                            }

                            // Check if modal is already initialized
                            let modal = bootstrap.Modal.getInstance(mappedModal);
                            if (!modal) {
                                // Initialize the modal with backdrop: true to ensure a backdrop is created
                                modal = new bootstrap.Modal(mappedModal, {
                                    backdrop: true, // Use a backdrop
                                    keyboard: true  // Allows ESC key to close
                                });
                            }

                            // Show the modal
                            modal.show();
                        } else {
                            console.error(`Mapped modal with ID ${mappedModalId} not found`);
                        }
                    } else {
                        console.error(`No mapping found for difficulty level: ${difficultyLevel}`);
                    }
                } else {
                    console.error('No difficulty text found in button');
                }

                return;
            }

            console.log(`Found modal element, showing it`);

            // Only clean up extra backdrops if there are more than one
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 1) {
                console.log(`Found ${backdrops.length} backdrops, cleaning up extras`);
                // Keep only the last backdrop (most recently added)
                for (let i = 0; i < backdrops.length - 1; i++) {
                    console.log(`Removing extra backdrop ${i+1}`);
                    backdrops[i].remove();
                }
            }

            // Check if modal is already initialized
            let modal = bootstrap.Modal.getInstance(modalElement);
            if (!modal) {
                // Initialize the modal with backdrop: true to ensure a backdrop is created
                modal = new bootstrap.Modal(modalElement, {
                    backdrop: true, // Use a backdrop
                    keyboard: true  // Allows ESC key to close
                });
            }

            // Show the modal
            modal.show();
        }, true);
    }

    // Function to fix Angular-generated modal targets
    function fixAngularModalTargets() {
        console.log('Fixing Angular-generated modal targets');

        // Find all difficulty buttons that might have Angular expressions
        const buttons = document.querySelectorAll('.difficulty-info-btn[data-bs-target^="#global-difficultyModal-{{"]');

        buttons.forEach(button => {
            // Get the difficulty text from the button
            const difficultyText = button.querySelector('.difficulty-text');
            if (!difficultyText) return;

            // Get the difficulty level
            const difficultyLevel = difficultyText.textContent.trim().toLowerCase();

            // Update the target attribute
            button.setAttribute('data-bs-target', `#global-difficultyModal-${difficultyLevel}`);
        });
    }

    // Add a MutationObserver to watch for Angular content changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any added nodes contain difficulty buttons
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.querySelector) {
                        const buttons = node.querySelectorAll('.difficulty-info-btn');
                        if (buttons.length > 0) {
                            fixAngularModalTargets();
                        }
                    }
                });
            }
        });
    });

    // Start observing the document body
    observer.observe(document.body, { childList: true, subtree: true });

    // Add a global event listener for ESC key to clean up backdrops
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Clean up backdrops when ESC is pressed
            setTimeout(cleanupOrphanedBackdrops, 150);
        }
    });

    // Add a global click handler to clean up backdrops when clicking outside modals
    document.addEventListener('click', function(e) {
        // If click is not inside a modal and not on a modal trigger
        if (!e.target.closest('.modal') && !e.target.closest('[data-bs-toggle="modal"]')) {
            // Clean up backdrops
            setTimeout(cleanupOrphanedBackdrops, 150);
        }
    });

    // Periodically check for orphaned backdrops (safety net)
    setInterval(cleanupOrphanedBackdrops, 5000);
});
