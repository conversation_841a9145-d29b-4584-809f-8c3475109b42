// Function to initialize difficulty modals - can be called on page load and after AJAX content is loaded
function initializeDifficultyModals(container = document) {
    /* console.log('Initializing difficulty modals within container:', container); */

    // Initialize modals
    const buttons = container.querySelectorAll('.difficulty-info-btn');
    /* console.log('Found difficulty buttons:', buttons.length); */

    buttons.forEach(function(button, index) {
        const modalId = button.getAttribute('data-bs-target');
        /* console.log(`Button ${index} modal target:`, modalId); */

        if (!modalId) {
            /* console.warn(`Button ${index} has no modal target`); */
            return;
        }

        const modalElement = document.querySelector(modalId);
        /* console.log(`Modal element for ${modalId}:`, modalElement); */

        if (!modalElement) {
            /* console.warn(`Modal element ${modalId} not found in DOM`); */
            return;
        }

        try {
            // Remove any existing click listeners to prevent duplicates
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            newButton.addEventListener('click', function(e) {
                /* console.log(`Button ${index} clicked`); */
                e.preventDefault();
                e.stopPropagation();

                // Direct fix for the modal z-index issue
                // First, find any existing modal backdrops and move them to the body
                const existingBackdrops = document.querySelectorAll('.modal-backdrop');
                existingBackdrops.forEach(backdrop => {
                    // Set a very low z-index for existing backdrops
                    backdrop.style.zIndex = '1040';
                });

                // Show the modal using Bootstrap's API
                const bsModal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                bsModal.show();

                // After the modal is shown, fix the z-index issues
                setTimeout(() => {
                    // Set the difficulty modal to a very high z-index
                    modalElement.style.zIndex = '9999';

                    // Find the newly created backdrop (should be the last one)
                    const allBackdrops = document.querySelectorAll('.modal-backdrop');
                    const newBackdrops = Array.from(allBackdrops).filter(backdrop =>
                        !Array.from(existingBackdrops).includes(backdrop)
                    );

                    if (newBackdrops.length > 0) {
                        // Move the new backdrop before the modal in the DOM
                        const lastBackdrop = newBackdrops[newBackdrops.length - 1];
                        lastBackdrop.style.zIndex = '9998';

                        // Move the backdrop to just before the modal
                        if (modalElement.parentNode) {
                            modalElement.parentNode.insertBefore(lastBackdrop, modalElement);
                        }
                    }

                    // Add a class to the body to indicate a difficulty modal is open
                    document.body.classList.add('difficulty-modal-open');

                    // Force repaint to ensure z-index changes take effect
                    modalElement.style.display = 'none';
                    modalElement.offsetHeight; // Force repaint
                    modalElement.style.display = '';
                }, 50);
            });

            // Add click handler to parent link to prevent bubbling
            const parentLink = newButton.closest('a');
            if (parentLink) {
                /* console.log(`Found parent link for button ${index}, adding click handler`); */
                newButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }, true);
            }

        } catch (error) {
            /* console.error(`Error initializing modal for ${modalId}:`, error); */
        }
    });
}

// Initialize all popovers
document.addEventListener('DOMContentLoaded', function() {
    /* console.log('DOM Content Loaded - Starting modal initialization'); */

    // Add custom styles
    const style = document.createElement('style');
    style.textContent = `
        .modal.fade .modal-dialog {
            transition: transform .2s ease-out;
        }
        .modal-content {
            border-radius: 10px;
        }
        .modal-header {
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 1.5rem;
        }
        .modal-body {
            padding: 1.5rem;
            font-size: 14px;
            line-height: 1.5;
        }
        /* Ensure difficulty modals appear above other modals and overlays */
        .difficulty-modal {
            z-index: 9999 !important; /* Extremely high z-index to ensure it's above everything */
        }

        /* When a difficulty modal is open, ensure its backdrop is above other modals */
        body.difficulty-modal-open .modal-backdrop.show {
            z-index: 9998 !important;
        }

        /* Fix for multiple backdrops - ensure they're properly stacked */
        .modal-backdrop.show {
            z-index: 1050 !important;
        }

        /* Override Bootstrap's modal styles to ensure our difficulty modals are visible */
        .difficulty-modal.show {
            display: block !important;
            opacity: 1 !important;
        }
    `;
    document.head.appendChild(style);

    // Add a global event listener for modal show events
    document.addEventListener('show.bs.modal', function(event) {
        const modal = event.target;

        // If this is a difficulty modal, ensure it's above everything else
        if (modal.classList.contains('difficulty-modal')) {
            console.log('Difficulty modal is being shown, applying z-index fixes');

            // Set an extremely high z-index for the difficulty modal
            modal.style.zIndex = '9999';

            // Mark the body to indicate a difficulty modal is open
            document.body.classList.add('difficulty-modal-open');

            // After a short delay, ensure the backdrop for this modal is correctly positioned
            setTimeout(() => {
                // Find the most recently added backdrop
                const backdrops = document.querySelectorAll('.modal-backdrop');
                if (backdrops.length > 0) {
                    const lastBackdrop = backdrops[backdrops.length - 1];
                    lastBackdrop.style.zIndex = '9998';

                    // Move the backdrop to just before the modal in the DOM
                    if (modal.parentNode) {
                        modal.parentNode.insertBefore(lastBackdrop, modal);
                    }
                }

                // Force repaint to ensure z-index changes take effect
                modal.style.display = 'none';
                modal.offsetHeight; // Force repaint
                modal.style.display = '';
            }, 50);
        }
    });

    // Add a global event listener for modal hide events
    document.addEventListener('hide.bs.modal', function(event) {
        const modal = event.target;

        // If this is a difficulty modal, clean up
        if (modal.classList.contains('difficulty-modal')) {
            // Remove the class from the body
            document.body.classList.remove('difficulty-modal-open');
        }
    });

    // Check if we're on the offline/maintenance login page - if so, don't initialize modals
    // Multiple detection methods for both zenbase and system offline templates
    const isOfflineLoginPage =
        // Method 1: Check for zenbase offline template markers
        document.body.classList.contains('offline-login-page') ||
        document.body.getAttribute('data-page-type') === 'offline-login' ||

        // Method 2: Check for system offline template structure
        (document.querySelector('#frame.outline') &&
         document.querySelector('#form-login') &&
         document.querySelectorAll('script').length < 5) || // System template has minimal scripts

        // Method 3: Check for specific offline page characteristics
        (document.querySelector('#frame.outline') &&
         document.querySelector('form[action*="index.php"]') &&
         document.querySelector('input[name="task"][value="user.login"]')) ||

        // Method 4: Check URL and page structure for root offline page
        (window.location.pathname === '/' &&
         document.querySelector('#frame.outline') &&
         document.querySelector('#form-login'));

    if (isOfflineLoginPage) {
        console.log('Offline login page detected in template.js - skipping difficulty modal initialization');

        // Add CSS to hide any modals that might have been created before this script ran
        const style = document.createElement('style');
        style.textContent = `
            .global-difficulty-modals,
            .difficulty-modal,
            [id*="difficultyModal"],
            [id*="global-difficultyModal"] {
                display: none !important;
            }
        `;
        document.head.appendChild(style);

        // Remove any existing modal containers
        setTimeout(function() {
            const modalContainers = document.querySelectorAll('.global-difficulty-modals, .difficulty-modal, [id*="difficultyModal"], [id*="global-difficultyModal"]');
            modalContainers.forEach(function(container) {
                container.remove();
            });
        }, 100);

        return;
    }

    // Initialize modals on page load
    initializeDifficultyModals();

    // Listen for Bootstrap tab show event to initialize modals in newly shown tabs
    document.addEventListener('shown.bs.tab', function(event) {
        /* console.log('Tab shown event triggered, initializing modals in new tab content'); */
        const tabContent = document.querySelector(event.target.getAttribute('href'));
        if (tabContent) {
            initializeDifficultyModals(tabContent);
        }
    });
});