<?php
defined('_JEXEC') or die;

// Don't render difficulty modals if we're on the offline login page
// Check if the current template file being used is an offline template
$app = JFactory::getApplication();

// Get the current template name and check if it's an offline template
$currentTemplate = $app->getTemplate();
$templateFile = $app->input->get('tmpl', '');

// Check if we're using offline.php template (either system or zenbase)
$isOfflineTemplate = false;

// Method 1: Check if offline template is being used
if (defined('JPATH_THEMES') && file_exists(JPATH_THEMES . '/' . $currentTemplate . '/offline.php')) {
    // Check if we're in offline mode and not logged in (typical offline page scenario)
    if ($app->get('offline') && !JFactory::getUser()->id) {
        $isOfflineTemplate = true;
    }
}

// Method 2: Check if system offline template is being used
if (file_exists(JPATH_THEMES . '/system/offline.php') && $app->get('offline') && !JFactory::getUser()->id) {
    $isOfflineTemplate = true;
}

// Method 3: Check for specific offline page indicators in the request
if (isset($_SERVER['SCRIPT_NAME']) &&
    (strpos($_SERVER['SCRIPT_NAME'], 'offline') !== false ||
     ($app->get('offline') && $_SERVER['REQUEST_URI'] === '/'))) {
    $isOfflineTemplate = true;
}

if ($isOfflineTemplate) {
    echo "<!-- Global difficulty modals skipped - offline login page detected -->\n";
    return;
}

// Define all difficulty levels with their descriptions and images
$difficultyLevels = [
    'gentle' => [
        'title' => 'Gentle',
        'description' => 'Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.',
        'image' => 'grade-1.svg'
    ],
    'easy' => [
        'title' => 'Easy',
        'description' => 'Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.',
        'image' => 'grade-1.svg'
    ],
    'moderate' => [
        'title' => 'Moderate',
        'description' => 'Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.',
        'image' => 'grade-2.svg'
    ],
    'challenging' => [
        'title' => 'Challenging',
        'description' => 'For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.',
        'image' => 'grade-3.svg'
    ],
    'hardcore' => [
        'title' => 'Hardcore',
        'description' => 'Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.',
        'image' => 'grade-4.svg'
    ]
];

// Check if we have a specific item with categories to use for descriptions
if (isset($item) && isset($item->categories)) {
    foreach ($difficultyLevels as $level => $data) {
        foreach ($item->categories as $category) {
            if (isset($category->parent) && $category->parent->alias == 'activity-level' && strtolower($category->title) == $level) {
                $difficultyLevels[$level]['description'] = $category->description;
                break;
            }
        }
    }
}

// Debug output
echo "<!-- Global difficulty modals included -->\n";
?>

<!-- Global Difficulty Modals - These will be placed outside the page container -->
<div class="global-difficulty-modals">
    <style>
    .difficulty-modal .modal-dialog {
        max-width: 400px;
    }

    .difficulty-modal .modal-content {
        border-radius: 24px;
        border: none;
        padding: 24px;
        background-color: #FFFFFF;
    }

    .difficulty-modal .modal-header {
        border: none;
        padding: 0;
        position: relative;
    }

    .difficulty-modal .btn-close {
        position: absolute;
        right: 15px;
        top: 15px;
        background: none;
        opacity: 1;
        width: 24px;
        height: 24px;
        padding: 0;
        z-index: 2;
    }

    .difficulty-modal .btn-close::before {
        content: "×";
        font-size: 32px;
        line-height: 24px;
        color: #000;
    }

    .difficulty-modal .modal-body {
        text-align: center;
        padding: 0;
    }

    .difficulty-modal .grade-image {
        width: 120px;
        height: 120px;
        margin: 24px auto;
    }

    .difficulty-modal .difficulty-title {
        font-size: 32px;
        font-weight: 500;
        margin-bottom: 24px;
        color: #000000;
    }

    .difficulty-modal .difficulty-description {
        font-size: 16px;
        line-height: 1.5;
        color: #333;
        margin-bottom: 32px;
    }

    .difficulty-modal .more-info-btn {
        background: #FE7720;
        color: white;
        border: 1px solid #FE7720;
        border-radius: 100px;
        padding: 11px 23px;
        font-size: 16px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s, border-color 0.2s;
        height: 48px;
        box-sizing: border-box;
    }

    .difficulty-modal .more-info-btn:hover {
        background: #e66b1c;
        border-color: #e66b1c;
    }

    .difficulty-modal .more-info-btn::after {
        content: "›";
        font-size: 20px;
        line-height: 1;
    }
    </style>

    <?php foreach ($difficultyLevels as $level => $data): ?>
    <div class="modal fade difficulty-modal" id="global-difficultyModal-<?php echo $level; ?>" tabindex="-1" aria-labelledby="difficultyModalLabel-<?php echo $level; ?>" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <img src="/templates/zenbase/images/grading/<?php echo $data['image']; ?>" alt="<?php echo $data['title']; ?> difficulty grade" class="grade-image">
                    <h2 class="difficulty-title"><?php echo $data['title']; ?></h2>
                    <div class="difficulty-description">
                        <?php echo $data['description']; ?>
                    </div>
                    <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                        More about difficulty ratings
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Add debug info -->
    <script>
    console.log('Global difficulty modals rendered with IDs:');
    <?php foreach ($difficultyLevels as $level => $data): ?>
    console.log('global-difficultyModal-<?php echo $level; ?>');
    <?php endforeach; ?>
    </script>
</div>
