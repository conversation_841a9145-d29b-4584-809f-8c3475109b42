<?php
  defined('_JEXEC') or die('Restricted access');
  jimport('mrzen.helpers.ZenURLHelper');
  jimport('mzelastic.helper');
  jimport('mrzen.helpers.ZenAngularHelper');

  // Load SPPB CSS manually to avoid order issues.
  //
  JLoader::register('SppagebuilderHelperSite', JPATH_SITE . '/components/com_sppagebuilder/helpers/helper.php');
  SppagebuilderHelperSite::addStylesheet('sppagebuilder.css');

  $app = JFactory::getApplication();
  $doc = & JFactory::getDocument();
  $template_dir = '/templates/'.$app->getTemplate().'/';

  // Add preview bullets loader script
  $doc->addScript($template_dir . 'js/preview-bullets-loader.js');

  $default_title = JText::_('ZEN_SEARCH_RESULTS_TITLE');
  $searchImg = $this->params->get('image');
  $regexPattern = "/w:.*?q/";
  $page_title = ($this->params->get('show_page_title')) ? $this->params->get('page_title') : $default_title;
  $filter = strlen($this->params->get('search')) > 0;
  $droot = ZenURLHelper::getOriginProtocol() . '://' . ZenURLHelper::getOriginHost();

  // Manually load breadcrumbs.
  //
  $breadcrumbs_mod = JModuleHelper::getModule('mod_breadcrumbs', '');
  $breadcrumbs_modParams = new JRegistry($breadcrumbs_mod->params);
  $breadcrumbs_modParams->set('style', '');
  $breadcrumbs_mod->showtitle = '0';
  $breadcrumbs_modRender = $doc->loadRenderer('module');
  $breadcrumbs_modHtml = $breadcrumbs_modRender->render($breadcrumbs_mod, array('params'=> $breadcrumbs_modParams));
?>
<!------------------------------------------------------------------------------
// Manually Load Breadcrumbs
//----------------------------------------------------------------------------->
<div class="zen-body__breadcrumbs">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="moduletable">
          <?php echo $breadcrumbs_modHtml; ?>
        </div>
      </div>
    </div>
  </div>
</div>
<?php
// Include global difficulty modals at root level, before Angular app
require(JPATH_BASE . '/templates/zenbase/html/partials/global_difficulty_modals.php');

// Add the difficulty modal handler script only if not in maintenance mode
use Joomla\CMS\Factory;
use Joomla\CMS\Uri\Uri;
$doc = Factory::getDocument();
$app = Factory::getApplication();
if (!$app->get('offline')) {
    $doc->addScript(Uri::base(true) . '/templates/zenbase/js/difficulty-modal-handler.js');
}
?>
<div data-ng-cloak data-ng-controller="FiltersController" class="search-page zen-search">
  <?php if ($filter) : ?>
    <div class="header">
      <div class="zen-hero hero-image">
        <div class="container">
          <div class="row">
            <div class="col-12">
              <div class="zen-hero__title">
                <h1 class="zen-title">
                  <?= strlen($this->params->get('page_heading')) > 0 ?
                      $this->params->get('page_heading')
                    :
                      $this->params->get('page_title');
                    ?>
                </h1>
              </div>
              <?php if (count($searchImg) > 0) : ?>
                <?php $mobile_xxs_image = preg_replace($regexPattern, 'w:400/h:475/q', $searchImg); ?>
                <?php $mobile_xs_image = preg_replace($regexPattern, 'w:568/h:415/q', $searchImg); ?>
                <?php $mobile_image = preg_replace($regexPattern, 'w:768/h:450/q', $searchImg); ?>
                <?php $tablet_image = preg_replace($regexPattern, 'w:992/h:500/q', $searchImg); ?>
                <?php $desktop_image = preg_replace($regexPattern, 'w:1600/h:600/q', $searchImg); ?>
                <?php $desktop_lg_image = preg_replace($regexPattern, 'w:1600/h:600/q', $searchImg); ?>
                <picture>
                  <source media="(min-width: 1200px)"
                    srcset="<?php echo $desktop_lg_image; ?>">
                  <source media="(min-width: 992px)"
                    srcset="<?php echo $desktop_image; ?>">
                  <source media="(min-width: 768px)"
                    srcset="<?php echo $tablet_image; ?>">
                  <source media="(min-width: 576px)"
                    srcset="<?php echo $mobile_image; ?>">
                  <source media="(min-width: 376px)"
                    srcset="<?php echo $mobile_xs_image; ?>">
                  <source media="(max-width: 375px)"
                    srcset="<?php echo $mobile_xxs_image; ?>">
                  <img class="img-fluid"
                    src="<?php echo $desktop_lg_image; ?>"
                    alt="<?php echo $this->params->get('page_title'); ?>">
                </picture>
              <?php endif; ?>
            </div>
          </div>
          <?php if (strlen($this->params->get('description')) > 0): ?>
            <div class="row justify-content-center">
              <div class="col-12 col-lg-8">
                <div class="zen-search__intro text-center my-5">
                  <?php echo $this->params->get('description'); ?>
                </div>
              </div>
            </div>
          <?php endif;?>
        </div>
      </div>
    </div>
  <?php endif; ?>
  <div id="holiday-search" class="zen-search__content">
    <div id="search-results" class="container">
      <div class="row">
        <?php if ($_COOKIE["winWidth"] <= 991) : ?>
          <?= $this->loadTemplate('filters') ?>
        <?php else : ?>
          <?= $this->loadTemplate('filters') ?>
        <?php endif; ?>
        <div class="col-lg-9" data-ng-view></div>
      </div>
    </div>
  </div>
</div>

<script id="monthPopup" type="text/ng-template">
  <div data-ng-if="currentMonth.operates" class="monthPopup" title="Departures in {{ currentMonth.month_name }}">
     <h4>Available dates:</h4>
     <ul>
        <li data-ng-repeat="date in currentMonth.dates">{{ date | date : 'dd/MM/yy' }} - {{ currentMonth.end_dates[$index] | date : 'dd/MM/yy' }}</li>
      </ul>
     </div>
</script>

<script id="holidays.html" type="text/ng-template">
  <?= $this->loadTemplate('holidays') ?>
</script>

<script id="dates.html" type="text/ng-template">
  <?= $this->loadTemplate('dates') ?>
</script>

<script>
  jQuery(function() {
    // Set correct switcher.
    //
    if (window.location.hash.indexOf("/dates") >= 0) {
      jQuery(".search-switcher a.current").removeClass("current");
      jQuery(".search-switcher a[href*=\"dates\"]").addClass("current");
    }

    jQuery(".search-switcher a").click(function() {
      var $a = jQuery(this);
      if (!$a.hasClass("current")) {
        setTimeout(function() {
          jQuery(".search-switcher a.current").removeClass("current");
          $a.addClass("current");
        }, 500);
      }
    });

    jQuery(".search-link").click(function(e) {
      jQuery(".reset-link").click();
    });

    jQuery(".report-range .reset-date").click(function() {});

    // Mobile date.
    //
    jQuery(".mobile-date input").change(function() {
      jQuery(".mobile-date div").removeClass("performed").addClass("changed");
    });

    jQuery(".mobile-date a.clear").click(function() {
      jQuery(".mobile-date input").val("");
      jQuery(".mobile-date div").attr("class", "");
    });

    jQuery(".mobile-date a.go").click(function() {
      jQuery("#facets").collapse("hide");
      jQuery(".mobile-date div").removeClass("changed").addClass("performed");
    });

    jQuery(".mobile-date input.init").click(function() {
      jQuery(this).removeClass("init");
    });

    jQuery(".selectlist select").change(function(e) {
      jQuery(this).siblings("span").html(jQuery(this).find("option:selected").html());
    });
  });
</script>

<style>
  @media screen and (max-width: 576px) {
    .zen-body__main {
        /* padding:120px 0 0!important; */
    }
}
  @media (min-width: 992px) {
.zen-header {
  margin-top: 38px;
  margin-bottom: 28px;
}
.zen-body__main {
    padding: 0;
}
  }

@media screen and (max-width:992px ) {

  .zen-header {
    position: static;
    /* margin-top: 1px; */
  }
    .zen-results-header {margin-top: 20px;}

  /* .zen-header__logo {padding: 23px 0; } */

}
  .dropdown-like-select .hidden,
  .dropdown-like-select .hidden:focus,
  .dropdown-like-select .hidden:active,
  .dropdown-like-select .hidden.focus {
    display: flex;
    align-items: center;
    /* gap: 0.5rem; */
    padding: 0 8px 0 18px;
    border: 1px solid white;
    border-radius:999px;
    color: white;
    text-decoration: none;
    text-transform: none;
    background-color: transparent;
    justify-content: space-between;
  }
  .dropdown-like-select .hidden::before {
    content: 'Sort by:';
    margin-right: 0.5rem;
  }
  .dropdown-like-select .hidden::after {
    content: '';
    display: inline-block;
    position: static;
    width: 24px;
    height: 25px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 70%;
    border: none;
    margin-left: 0;
    vertical-align: middle;
    background-color: transparent;
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='25' viewBox='0 0 24 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_1060_3236)'%3E%3Cpath d='M16.59 9.09009L12 13.6701L7.41 9.09009L6 10.5001L12 16.5001L18 10.5001L16.59 9.09009Z' fill='white'%3E%3C/path%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1060_3236'%3E%3Crect width='24' height='24' fill='white' transform='translate(0 0.5)'%3E%3C/rect%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
  }
  .dropdown-like-select::after {
    content: none;
  }
  .selectlist select {
    height: auto;
  }

  #search-results .zen-card .zen-link {
    width: 100%;
  }
</style>